/*********************************************************************************
// This code is created by SimCoder Version 2025a15.147 for F28004x Hardware Target
//
// SimCoder is copyright by Powersim Inc., 2009-2021
//
// Date: July 30, 2025 17:52:13
**********************************************************************************/
#include	<math.h>
#include	"PS_bios.h"
#define	GetCurTime() PS_GetSysTimer()
#define	iif(a, b, c) ((a) ? (b) : (c))
typedef interrupt void (*ClaIntr)(void);
ClaIntr Cla1Task1 = 0;
ClaIntr Cla1Task2 = 0;
ClaIntr Cla1Task3 = 0;
ClaIntr Cla1Task4 = 0;
ClaIntr Cla1Task5 = 0;
ClaIntr Cla1Task6 = 0;
ClaIntr Cla1Task7 = 0;
ClaIntr Cla1Task8 = 0;




interrupt void Task();

#ifdef _FLASH
#pragma DATA_SECTION(PSK_SysClk, "copysections")
#endif
const Uint16 PSK_SysClk = 100;  // MHz
extern	DefaultType	fGblia;
extern	DefaultType	fGblid;
extern	DefaultType	fGbliq;
extern	DefaultType	fGblidref;
extern	DefaultType	fGbltheta;
extern	DefaultType	fGblV4;
extern	DefaultType	fGblV5;
extern	DefaultType	fGblV6;

#define	PSM_VRefHiA		3.3		// ADC-A VREFHIA
#define	PSM_VRefHiB		3.3		// ADC-B VREFHIB
#define	PSM_VRefHiC		3.3		// ADC-C VREFHIC






DefaultType	fGblia = 0;
DefaultType	fGblid = 0;
DefaultType	fGbliq = 0;
DefaultType	fGblidref = 0;
DefaultType	fGbltheta = 0;
DefaultType	fGblV4 = 0;
DefaultType	fGblV5 = 0;
DefaultType	fGblV6 = 0;
interrupt void Task()
{
	DefaultType	fDQO4_2, fDQO4_1, fDQO4, fP55, fS3, fSUM20, fC2, fP54, fS2;
	DefaultType	fSUM24, fLIM1, fS1, fSUM22, fMULT4, fVgain, fZOH1, fPSM_F28004x_ADC1;
	DefaultType	fC1, fABC1_2, fABC1_1, fABC1, fATAN21, fABC_AB1_1, fABC_AB1;
	DefaultType	fMULT10, fVgain6, fZOH7, fSUMP6, fPSM_F28004x_ADC1_14, fMULT9;
	DefaultType	fVgain2, fZOH6, fSUMP5, fPSM_F28004x_ADC1_4, fMULT8, fVgain1;
	DefaultType	fZOH5, fSUMP4, fVDC2, fPSM_F28004x_ADC1_2, fMULT7, fVgain4;
	DefaultType	fZOH4, fPSM_F28004x_ADC1_8, fMULT6, fVgain3, fZOH3, fPSM_F28004x_ADC1_3;
	DefaultType	fMULT5, fVgain5, fZOH2, fPSM_F28004x_ADC1_1;

	ADC_CLR(2) = 1 << (1-1);	// Clear ADC interrupt flag 1
	CPU_PIEACK |= M__INT1;

	fPSM_F28004x_ADC1_1 = ADC_RESULT(2, 1) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH2 = fPSM_F28004x_ADC1_1;
	fVgain5 = 1.0/0.264;
	fMULT5 = fZOH2 * fVgain5;
#ifdef	_DEBUG
	fGblia = fMULT5;
#endif
	fPSM_F28004x_ADC1_3 = ADC_RESULT(2, 3) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH3 = fPSM_F28004x_ADC1_3;
	fVgain3 = 1.0/0.264;
	fMULT6 = fZOH3 * fVgain3;
	fPSM_F28004x_ADC1_8 = ADC_RESULT(2, 5) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH4 = fPSM_F28004x_ADC1_8;
	fVgain4 = 1.0/0.264;
	fMULT7 = fZOH4 * fVgain4;
	fPSM_F28004x_ADC1_2 = ADC_RESULT(2, 2) * (1.0 * PSM_VRefHiC / 4096.0);
	fVDC2 = (-1.5);
	fSUMP4 = fPSM_F28004x_ADC1_2 + fVDC2;
	fZOH5 = fSUMP4;
	fVgain1 = 33.571;
	fMULT8 = fZOH5 * fVgain1;
	fPSM_F28004x_ADC1_4 = ADC_RESULT(2, 4) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP5 = fPSM_F28004x_ADC1_4 + fVDC2;
	fZOH6 = fSUMP5;
	fVgain2 = 33.571;
	fMULT9 = fZOH6 * fVgain2;
	fPSM_F28004x_ADC1_14 = @AdcDcResult15@;
	fSUMP6 = fPSM_F28004x_ADC1_14 + fVDC2;
	fZOH7 = fSUMP6;
	fVgain6 = 33.571;
	fMULT10 = fZOH7 * fVgain6;
	// ABC to alpha/beta transformation
	fABC_AB1 = 0.81649658 * (fMULT8 - (fMULT9 + fMULT10) * 0.5);
	fABC_AB1_1 = 0.70710678 * (fMULT9 - fMULT10); // uvw2ab
	fATAN21 = atan2(fABC_AB1_1, fABC_AB1);
	// ABC to DQ transformation
	fABC1 = 2.0/3.0 * (cos(fATAN21) * fMULT5 + cos(fATAN21-2*3.14159265/3) * fMULT6 + cos(fATAN21+2*3.14159265/3) * fMULT7);
	fABC1_1 = 2.0/3.0 * (sin(fATAN21) * fMULT5 + sin(fATAN21-2*3.14159265/3) * fMULT6 + sin(fATAN21+2*3.14159265/3) * fMULT7);
	fABC1_2 = (fMULT5 + fMULT6 + fMULT7) / 3.0;
#ifdef	_DEBUG
	fGblid = fABC1;
#endif

#ifdef	_DEBUG
	fGbliq = fABC1_1;
#endif

	fC1 = 50;
	fPSM_F28004x_ADC1 = ADC_RESULT(2, 0) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH1 = fPSM_F28004x_ADC1;
	fVgain = 33.571;
	fMULT4 = fZOH1 * fVgain;
	fSUM22 = fC1 - fMULT4;
	{	// backward Euler
		static DefaultType out_A = 0.0;
		fS1 = out_A + (0.02/(0.0002*20000)) * fSUM22;
		fS1 = (fS1 < (-15)) ? (-15) : ((fS1 > 15.0) ? 15.0 : fS1);
		out_A = fS1;
		fS1 += 0.02 * fSUM22;
		fS1 = (fS1 < (-15)) ? (-15) : ((fS1 > 15.0) ? 15.0 : fS1);
	}
	fLIM1 = (fS1 > 10) ? 10 : ((fS1 < 0) ? 0 : fS1);
#ifdef	_DEBUG
	fGblidref = fLIM1;
#endif
#ifdef	_DEBUG
	fGbltheta = fATAN21;
#endif
	fSUM24 = fLIM1 - fABC1;
	{	// backward Euler
		static DefaultType out_A = 0.0;
		fS2 = out_A + (0.2215/(0.0002*20000)) * fSUM24;
		fS2 = (fS2 < (-10)) ? (-10) : ((fS2 > 10.0) ? 10.0 : fS2);
		out_A = fS2;
		fS2 += 0.2215 * fSUM24;
		fS2 = (fS2 < (-10)) ? (-10) : ((fS2 > 10.0) ? 10.0 : fS2);
	}
	fP54 = fS2 * (-2);
	fC2 = 0;
	fSUM20 = fC2 - fABC1_1;
	{	// backward Euler
		static DefaultType out_A = 0.0;
		fS3 = out_A + (0.2215/(0.0002*20000)) * fSUM20;
		fS3 = (fS3 < (-10)) ? (-10) : ((fS3 > 10.0) ? 10.0 : fS3);
		out_A = fS3;
		fS3 += 0.2215 * fSUM20;
		fS3 = (fS3 < (-10)) ? (-10) : ((fS3 > 10.0) ? 10.0 : fS3);
	}
	fP55 = fS3 * (-2);
	// DQ to ABC transformation
	fDQO4 = cos(fATAN21) * fP54 + sin(fATAN21) * fP55 + 0;
	fDQO4_1 = cos(fATAN21 - 2*3.14159265/3) * fP54 + sin(fATAN21 - 2*3.14159265/3) * fP55 + 0;
	fDQO4_2 = cos(fATAN21 + 2*3.14159265/3) * fP54 + sin(fATAN21 + 2*3.14159265/3) * fP55 + 0;
#ifdef	_DEBUG
	fGblV4 = fMULT4;
#endif
#ifdef	_DEBUG
	fGblV5 = fMULT4;
#endif
#ifdef	_DEBUG
	fGblV6 = fC1;
#endif
	// Start of changing PWM3ph1 registers
	// Set Duty Cycle of U
	PWM_CMPA(1) = ((Uint32)(PWM_TBPRD(1))+1) * (__fsat(fDQO4, 2 + (-1), (-1)) - (-1)) * (1.0/2);
	PWM_CMPA(2) = ((Uint32)(PWM_TBPRD(1))+1) * (__fsat(fDQO4_1, 2 + (-1), (-1)) - (-1)) * (1.0/2);
	PWM_CMPA(3) = ((Uint32)(PWM_TBPRD(1))+1) * (__fsat(fDQO4_2, 2 + (-1), (-1)) - (-1)) * (1.0/2);
	// End of changing PWM3ph1 registers
}


void Initialize(void)
{
	PS_SysInit(2, 20);
	PS_PwmStartStopClock(0);	// Stop Pwm Clock
	PS_TimerInit(0, 0);
	PS_SetVREF(0, 1, 0);	// Set external VRef for ADC-A
	PS_SetVREF(1, 1, 0);	// Set external VRef for ADC-B
	PS_SetVREF(2, 1, 0);	// Set external VRef for ADC-C

	{
	    int i, preAdcNo = -1;
	    /* PST_AdcAttr: Adc No., Channel No., Soc No., Trig Src, SampleTime(clock) */
	    const PST_AdcAttr aryAdcInit[7] = {
			{2, 0, 0, ADCTRIG_PWM1, 32},
			{2, 1, 1, ADCTRIG_PWM1, 32},
			{2, 2, 2, ADCTRIG_PWM1, 32},
			{2, 3, 3, ADCTRIG_PWM1, 32},
			{2, 4, 4, ADCTRIG_PWM1, 32},
			{2, 8, 5, ADCTRIG_PWM1, 32},
			{2, 14, 6, ADCTRIG_PWM1, 32}};
	    const PST_AdcAttr *p = aryAdcInit;
	    for (i = 0; i < 7; i++, p++) {
	        if (preAdcNo != p->nAdcNo) {
	            PS_AdcInit(p->nAdcNo);
	            preAdcNo = p->nAdcNo;
	        }
	        PS_AdcSetChn(p->nAdcNo, p->nChnNo, p->nSocNo, p->nTrigSrc, p->nWindSz);
	    }
	}

	PS_Pwm3phInit(1, 0, 0, 1.e6/(20000*1.0), ePwmStartHigh1, ePwmComplement, HRPWM_DISABLE);	// pwmNo, seqNo, wave type, period, PwmA, PWMB, UseHRPwm
	PS_PwmSetDeadBand(1, 0, 2, 3, 0, 1, 1);
	PS_PwmSetDeadBand(2, 0, 2, 3, 0, 1, 1);
	PS_PwmSetDeadBand(3, 0, 2, 3, 0, 1, 1);
	PS_PwmSetIntrType(1, ePwmIntrAdc, 1, 0);
	PS_AdcSetIntr(2, 1, 6, Task); // AdcNo, IntrNo, SocNo, Interrupt Vector
	PS_PwmSetTripAction(1, eTzHiZ, eTzHiZ);
	PS_PwmSetTripAction(2, eTzHiZ, eTzHiZ);
	PS_PwmSetTripAction(3, eTzHiZ, eTzHiZ);
	PWM_CMPA(1) = (0 - (-1)) / (1.0 * 2) * PWM_TBPRD(1);
	PWM_CMPA(2) = (0 - (-1)) / (1.0 * 2) * PWM_TBPRD(1);
	PWM_CMPA(3) = (0 - (-1)) / (1.0 * 2) * PWM_TBPRD(1);
	PSM_Pwm3phStart(1);       // Start Pwm3ph1 at the beginning

	PS_PwmStartStopClock(1);	// Start Pwm Clock
}


void main()
{
	Initialize();
	PSM_EnableIntr();   // Enable Global interrupt INTM
	PSM_EnableDbgm();
	for (;;) {
	}
}

